# Comprehensive Leads Management System

## Overview

The Comprehensive Leads Management System replaces the previous simple status-based workflow with a sophisticated call management and group assignment process. This system provides better tracking, automated workflows, and improved data integrity.

## Key Features

### 🔄 **Simplified Workflow**
- **NEW** → **CALLING** → **CALL_COMPLETED** → **GROUP_ASSIGNED** → **ARCHIVED**
- Removed unnecessary intermediate statuses (CONTACTED, INTERESTED, ENROLLED)
- Clear progression with specific actions at each stage

### 📞 **Call Management**
- **Start Call**: Automatically begins call recording and timer
- **Call Timer**: Real-time display with 5-minute auto-cutoff
- **End Call**: Manual or automatic call termination
- **Call Records**: Detailed logging of all call activities
- **Notes**: Add call notes during or after the call

### 👥 **Group Assignment**
- **Smart Filtering**: Filter groups by teacher, level, and search terms
- **Capacity Management**: Only shows groups with available spots
- **Detailed Information**: Course, teacher, schedule, and room details
- **Assignment Tracking**: Records when and who assigned the group

### 📁 **Archive System**
- **Automatic Archiving**: Leads move to archive after group assignment
- **Separate View**: Dedicated archive tab for completed leads
- **Data Retention**: 30-day automatic cleanup of archived leads
- **Audit Trail**: Complete history of lead progression

### 📅 **Advanced Filtering**
- **Date Filters**: Today, Yesterday, Last 7 days, Last 30 days, Custom range
- **Status Filters**: Filter by current lead status
- **Real-time Updates**: Automatic refresh on status changes

## Database Schema Changes

### Lead Model Updates
```prisma
model Lead {
  // ... existing fields
  
  // Call management fields
  callStartedAt    DateTime?
  callEndedAt      DateTime?
  callDuration     Int?       // in seconds
  
  // Group assignment fields
  assignedGroupId  String?
  assignedTeacherId String?
  assignedAt       DateTime?
  
  // Archive management
  archivedAt       DateTime?
  
  // Relations
  assignedGroup    Group?     @relation(fields: [assignedGroupId], references: [id])
  assignedTeacher  Teacher?   @relation(fields: [assignedTeacherId], references: [id])
  callRecords      CallRecord[]
}
```

### New CallRecord Model
```prisma
model CallRecord {
  id          String   @id @default(cuid())
  leadId      String
  userId      String   // User who made the call
  startedAt   DateTime
  endedAt     DateTime?
  duration    Int?     // in seconds
  notes       String?
  recordingUrl String? // URL to call recording if available
  
  // Relations
  lead        Lead     @relation(fields: [leadId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id])
}
```

### Updated LeadStatus Enum
```prisma
enum LeadStatus {
  NEW
  CALLING
  CALL_COMPLETED
  GROUP_ASSIGNED
  ARCHIVED
  NOT_INTERESTED
}
```

## API Endpoints

### Call Management
- `POST /api/leads/[id]/call` - Start a call
- `PUT /api/leads/[id]/call` - End a call

### Group Assignment
- `GET /api/leads/[id]/assign-group` - Get available groups
- `POST /api/leads/[id]/assign-group` - Assign lead to group

### Archive Management
- `POST /api/leads/[id]/archive` - Archive a lead
- `DELETE /api/leads/[id]/archive` - Unarchive a lead (Admin only)

### Data Cleanup
- `GET /api/leads/cleanup` - Preview cleanup (Admin only)
- `POST /api/leads/cleanup` - Execute cleanup (Admin only)

### Enhanced Leads API
- `GET /api/leads?dateFilter=today&status=NEW&archived=false`
- Support for date filtering, status filtering, and archive separation

## User Interface Components

### DateFilter Component
- Preset date ranges (Today, Yesterday, Last 7 days, Last 30 days)
- Custom date range picker
- Clear filter functionality

### CallManager Component
- Start/End call buttons
- Real-time timer display
- Call notes input
- Recording indicator
- Auto-end at 5 minutes

### GroupAssignmentModal Component
- Search and filter groups
- Teacher and level filters
- Group capacity display
- Assignment notes
- Real-time availability

### LeadsList Component
- List-style layout (replaces card grid)
- Status-based action buttons
- Call duration display
- Group assignment details
- Archive functionality

## Role-Based Access Control

### Permissions
- **ADMIN**: Full access including cleanup and unarchive
- **MANAGER**: Full leads management access
- **RECEPTION**: Standard leads management access
- **Other roles**: No access to leads management

### Security Features
- Session-based authentication
- Role validation on all API endpoints
- Activity logging for audit trails
- No delete functionality (archive only)

## Data Integrity & Retention

### No-Delete Policy
- No delete buttons or functionality
- All data preserved through archiving
- Admin-only unarchive capability

### 30-Day Retention
- Automatic cleanup of archived leads after 30 days
- Cleanup preview for administrators
- Cascade deletion of related call records
- Activity logging of cleanup operations

### Audit Trail
- Complete tracking of status changes
- Call record preservation
- Group assignment history
- User action logging

## Migration Instructions

### Database Migration
```bash
npx prisma migrate dev --name comprehensive-leads-management
```

### Required Environment Variables
No additional environment variables required.

### Post-Migration Steps
1. Update existing leads to new status format (if needed)
2. Train staff on new workflow
3. Set up automated cleanup schedule (optional)

## Usage Instructions

### For Reception Staff

#### Processing New Leads
1. **View Leads**: Navigate to Leads Management
2. **Filter**: Use date and status filters to find relevant leads
3. **Start Call**: Click "Call" button for NEW leads
4. **During Call**: Add notes and monitor timer
5. **End Call**: Click "End Call" or wait for auto-end
6. **Assign Group**: Click "Choose Group" after call completion
7. **Archive**: System automatically archives after group assignment

#### Using Filters
- **Date Filter**: Select preset ranges or custom dates
- **Status Filter**: Filter by lead progression status
- **Archive View**: Switch to archive tab for completed leads

### For Administrators

#### Data Management
- **Cleanup Preview**: View leads eligible for deletion
- **Execute Cleanup**: Remove archived leads older than 30 days
- **Unarchive**: Restore archived leads if needed
- **Activity Logs**: Monitor all lead management activities

## Technical Implementation

### Frontend Components
- React with TypeScript
- Tailwind CSS for styling
- Shadcn/ui component library
- Real-time state management

### Backend Architecture
- Next.js API routes
- Prisma ORM for database operations
- Zod for input validation
- NextAuth for authentication

### Database Design
- PostgreSQL with Prisma
- Foreign key relationships
- Cascade deletion for cleanup
- Indexed fields for performance

## Performance Considerations

### Optimization Features
- Pagination for large lead lists
- Efficient database queries with includes
- Client-side filtering for better UX
- Lazy loading of group data

### Monitoring
- Activity logging for audit trails
- Error handling and user feedback
- Performance tracking for API calls

## Future Enhancements

### Planned Features
- Call recording integration
- SMS/Email notifications
- Advanced analytics dashboard
- Bulk operations for leads
- Integration with external CRM systems

### Scalability
- Database indexing optimization
- Caching for frequently accessed data
- Background job processing for cleanup
- API rate limiting for protection

## Support & Maintenance

### Regular Tasks
- Monitor cleanup operations
- Review activity logs
- Update group availability
- Train new staff on workflow

### Troubleshooting
- Check database connections
- Verify role permissions
- Review error logs
- Validate API responses

For technical support or feature requests, contact the development team.
