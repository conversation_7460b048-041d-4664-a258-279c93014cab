import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '6months'

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    
    switch (timeRange) {
      case '1month':
        startDate.setMonth(now.getMonth() - 1)
        break
      case '3months':
        startDate.setMonth(now.getMonth() - 3)
        break
      case '6months':
        startDate.setMonth(now.getMonth() - 6)
        break
      case '1year':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setMonth(now.getMonth() - 6)
    }

    // Get basic counts
    const [totalStudents, totalGroups, totalCourses] = await Promise.all([
      prisma.student.count(),
      prisma.group.count({ where: { isActive: true } }),
      prisma.course.count({ where: { isActive: true } }),
    ])

    // Get total revenue
    const paymentsSum = await prisma.payment.aggregate({
      where: {
        status: 'PAID',
        paidDate: {
          gte: startDate,
          lte: now,
        },
      },
      _sum: {
        amount: true,
      },
    })

    const totalRevenue = Number(paymentsSum._sum.amount) || 0

    // Get monthly revenue data
    const monthlyPayments = await prisma.payment.findMany({
      where: {
        status: 'PAID',
        paidDate: {
          gte: startDate,
          lte: now,
        },
      },
      select: {
        amount: true,
        paidDate: true,
      },
    })

    // Process monthly revenue
    const monthlyRevenue = []
    const months = []
    for (let i = 5; i >= 0; i--) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      months.push({
        month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        monthStart: new Date(date.getFullYear(), date.getMonth(), 1),
        monthEnd: new Date(date.getFullYear(), date.getMonth() + 1, 0),
      })
    }

    for (const month of months) {
      const revenue = monthlyPayments
        .filter(payment => {
          const paidDate = new Date(payment.paidDate!)
          return paidDate >= month.monthStart && paidDate <= month.monthEnd
        })
        .reduce((sum, payment) => sum + Number(payment.amount), 0)
      
      monthlyRevenue.push({
        month: month.month,
        revenue,
      })
    }

    // Get enrollments by level
    const enrollmentsByLevel = await prisma.enrollment.groupBy({
      by: ['studentId'],
      where: {
        status: 'ACTIVE',
      },
      _count: {
        studentId: true,
      },
    })

    // Get student levels for active enrollments
    const studentLevels = await prisma.student.groupBy({
      by: ['level'],
      _count: {
        level: true,
      },
    })

    const enrollmentsByLevelData = studentLevels.map(item => ({
      level: item.level.replace('_', ' '),
      count: item._count.level,
    }))

    // Get payments by method
    const paymentsByMethod = await prisma.payment.groupBy({
      by: ['method'],
      where: {
        status: 'PAID',
        paidDate: {
          gte: startDate,
          lte: now,
        },
      },
      _sum: {
        amount: true,
      },
    })

    const paymentsByMethodData = paymentsByMethod.map(item => ({
      method: item.method.replace('_', ' '),
      amount: Number(item._sum.amount) || 0,
    }))

    // Get lead conversion data
    const leadConversion = []
    for (const month of months) {
      const leads = await prisma.lead.count({
        where: {
          createdAt: {
            gte: month.monthStart,
            lte: month.monthEnd,
          },
        },
      })

      const conversions = await prisma.lead.count({
        where: {
          status: 'ARCHIVED',
          updatedAt: {
            gte: month.monthStart,
            lte: month.monthEnd,
          },
        },
      })

      leadConversion.push({
        month: month.month,
        leads,
        conversions,
      })
    }

    const analytics = {
      totalStudents,
      totalRevenue,
      totalGroups,
      totalCourses,
      monthlyRevenue,
      enrollmentsByLevel: enrollmentsByLevelData,
      paymentsByMethod: paymentsByMethodData,
      leadConversion,
    }

    return NextResponse.json(analytics)
  } catch (error) {
    console.error('Error fetching analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
